// Three.js Coin Renderer with Realistic Physics
class CoinRenderer {
    constructor(canvasId) {
        this.canvas = document.getElementById(canvasId);
        this.isFlipping = false;
        this.animationId = null;
        
        // Animation state
        this.coinRotationX = 0;
        this.coinRotationY = 0;
        this.coinRotationZ = 0;
        this.coinPositionY = 0;
        this.velocity = { x: 0, y: 0, z: 0 };
        this.angularVelocity = { x: 0, y: 0, z: 0 };
        this.gravity = -0.015;
        this.bounceCount = 0;
        this.maxBounces = 3;
        this.bounceDamping = 0.6;
        this.rotationDamping = 0.98;
        
        this.initThreeJS();
        this.createCoin();
        this.setupLighting();
        this.startRenderLoop();
        this.setupEventListeners();
    }

    initThreeJS() {
        // Scene setup
        this.scene = new THREE.Scene();
        this.scene.background = null; // Transparent background
        
        // Camera setup
        this.camera = new THREE.PerspectiveCamera(
            75, 
            this.canvas.clientWidth / this.canvas.clientHeight, 
            0.1, 
            1000
        );
        this.camera.position.set(0, 0, 3);
        
        // Renderer setup
        this.renderer = new THREE.WebGLRenderer({ 
            canvas: this.canvas, 
            alpha: true,
            antialias: true 
        });
        this.renderer.setSize(this.canvas.clientWidth, this.canvas.clientHeight);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        this.renderer.setClearColor(0x000000, 0); // Transparent
    }

    createCoin() {
        // Coin geometry - cylinder for 3D coin
        const geometry = new THREE.CylinderGeometry(0.8, 0.8, 0.1, 32);
        
        // Create textures for heads and tails
        const headsTexture = this.createHeadsTexture();
        const tailsTexture = this.createTailsTexture();
        const edgeTexture = this.createEdgeTexture();
        
        // Materials array for different faces
        const materials = [
            new THREE.MeshPhongMaterial({ map: edgeTexture }), // Side
            new THREE.MeshPhongMaterial({ map: headsTexture }), // Top (Heads)
            new THREE.MeshPhongMaterial({ map: tailsTexture })  // Bottom (Tails)
        ];
        
        // Create coin mesh
        this.coin = new THREE.Mesh(geometry, materials);
        this.coin.castShadow = true;
        this.coin.receiveShadow = true;
        
        // Initial position
        this.coin.position.set(0, 0, 0);
        this.scene.add(this.coin);
    }

    createHeadsTexture() {
        const canvas = document.createElement('canvas');
        canvas.width = 256;
        canvas.height = 256;
        const ctx = canvas.getContext('2d');
        
        // Gold gradient background
        const gradient = ctx.createRadialGradient(128, 128, 0, 128, 128, 128);
        gradient.addColorStop(0, '#FFD700');
        gradient.addColorStop(0.7, '#FFA500');
        gradient.addColorStop(1, '#B8860B');
        
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 256, 256);
        
        // Border
        ctx.strokeStyle = '#8B4513';
        ctx.lineWidth = 8;
        ctx.beginPath();
        ctx.arc(128, 128, 120, 0, Math.PI * 2);
        ctx.stroke();
        
        // "H" text
        ctx.fillStyle = '#8B4513';
        ctx.font = 'bold 120px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText('หัว', 128, 128);
        
        const texture = new THREE.CanvasTexture(canvas);
        texture.wrapS = THREE.ClampToEdgeWrapping;
        texture.wrapT = THREE.ClampToEdgeWrapping;
        return texture;
    }

    createTailsTexture() {
        const canvas = document.createElement('canvas');
        canvas.width = 256;
        canvas.height = 256;
        const ctx = canvas.getContext('2d');
        
        // Silver gradient background
        const gradient = ctx.createRadialGradient(128, 128, 0, 128, 128, 128);
        gradient.addColorStop(0, '#E6E6E6');
        gradient.addColorStop(0.7, '#C0C0C0');
        gradient.addColorStop(1, '#A0A0A0');
        
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 256, 256);
        
        // Border
        ctx.strokeStyle = '#2C2C2C';
        ctx.lineWidth = 8;
        ctx.beginPath();
        ctx.arc(128, 128, 120, 0, Math.PI * 2);
        ctx.stroke();
        
        // "T" text
        ctx.fillStyle = '#2C2C2C';
        ctx.font = 'bold 120px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText('ก้อย', 128, 128);
        
        const texture = new THREE.CanvasTexture(canvas);
        texture.wrapS = THREE.ClampToEdgeWrapping;
        texture.wrapT = THREE.ClampToEdgeWrapping;
        return texture;
    }

    createEdgeTexture() {
        const canvas = document.createElement('canvas');
        canvas.width = 256;
        canvas.height = 32;
        const ctx = canvas.getContext('2d');
        
        // Gold edge color
        const gradient = ctx.createLinearGradient(0, 0, 0, 32);
        gradient.addColorStop(0, '#FFD700');
        gradient.addColorStop(0.5, '#FFA500');
        gradient.addColorStop(1, '#FFD700');
        
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 256, 32);
        
        const texture = new THREE.CanvasTexture(canvas);
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.ClampToEdgeWrapping;
        return texture;
    }

    setupLighting() {
        // Ambient light
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);
        
        // Directional light (main light)
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(2, 4, 2);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 1024;
        directionalLight.shadow.mapSize.height = 1024;
        this.scene.add(directionalLight);
        
        // Point light for extra shine
        const pointLight = new THREE.PointLight(0xffd700, 0.3, 10);
        pointLight.position.set(-2, 2, 2);
        this.scene.add(pointLight);
    }

    setupEventListeners() {
        // Handle canvas resize
        window.addEventListener('resize', () => {
            this.handleResize();
        });
        
        // Handle canvas click for pulse effect
        this.canvas.addEventListener('click', () => {
            if (!this.isFlipping) {
                this.pulseEffect();
            }
        });
    }

    handleResize() {
        const width = this.canvas.clientWidth;
        const height = this.canvas.clientHeight;
        
        this.camera.aspect = width / height;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(width, height);
    }

    pulseEffect() {
        const originalScale = this.coin.scale.clone();
        const targetScale = originalScale.clone().multiplyScalar(1.1);
        
        // Animate scale up and down
        const duration = 300;
        const startTime = Date.now();
        
        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            let scale;
            if (progress < 0.5) {
                // Scale up
                const t = progress * 2;
                scale = originalScale.clone().lerp(targetScale, t);
            } else {
                // Scale down
                const t = (progress - 0.5) * 2;
                scale = targetScale.clone().lerp(originalScale, t);
            }
            
            this.coin.scale.copy(scale);
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                this.coin.scale.copy(originalScale);
            }
        };
        
        animate();
    }

    startRenderLoop() {
        const render = () => {
            this.animationId = requestAnimationFrame(render);
            
            if (this.isFlipping) {
                this.updatePhysics();
            }
            
            this.renderer.render(this.scene, this.camera);
        };
        render();
    }

    updatePhysics() {
        // Update rotation with angular velocity
        this.coinRotationX += this.angularVelocity.x;
        this.coinRotationY += this.angularVelocity.y;
        this.coinRotationZ += this.angularVelocity.z;
        
        // Apply rotation damping
        this.angularVelocity.x *= this.rotationDamping;
        this.angularVelocity.y *= this.rotationDamping;
        this.angularVelocity.z *= this.rotationDamping;
        
        // Update position with velocity
        this.coinPositionY += this.velocity.y;
        this.velocity.y += this.gravity;
        
        // Handle bouncing
        if (this.coinPositionY <= -1.5 && this.velocity.y < 0) {
            this.coinPositionY = -1.5;
            this.velocity.y = -this.velocity.y * this.bounceDamping;
            this.bounceCount++;
            
            // Add some random rotation on bounce
            this.angularVelocity.x += (Math.random() - 0.5) * 0.1;
            this.angularVelocity.z += (Math.random() - 0.5) * 0.1;
            
            // Stop bouncing after max bounces and low velocity
            if (this.bounceCount >= this.maxBounces && Math.abs(this.velocity.y) < 0.05) {
                this.velocity.y = 0;
                this.coinPositionY = -1.5;
                this.stopFlipping();
            }
        }
        
        // Apply transformations to coin
        this.coin.rotation.set(this.coinRotationX, this.coinRotationY, this.coinRotationZ);
        this.coin.position.y = this.coinPositionY;
    }

    async flipCoin(result) {
        if (this.isFlipping) return;
        
        this.isFlipping = true;
        this.bounceCount = 0;
        
        // Reset position and rotation
        this.coinPositionY = 0;
        this.coinRotationX = 0;
        this.coinRotationY = 0;
        this.coinRotationZ = 0;
        
        // Set initial velocities for realistic flip
        this.velocity.y = 0.3; // Upward velocity
        
        // Calculate target rotation based on result
        const baseRotations = 5; // Number of full rotations
        const targetRotationY = result === 'heads' 
            ? baseRotations * Math.PI * 2 
            : (baseRotations * Math.PI * 2) + Math.PI;
        
        // Set angular velocities
        this.angularVelocity.y = targetRotationY / 120; // Spread over ~2 seconds
        this.angularVelocity.x = (Math.random() - 0.5) * 0.2; // Random tilt
        this.angularVelocity.z = (Math.random() - 0.5) * 0.1; // Random wobble
        
        // Return promise that resolves when animation completes
        return new Promise((resolve) => {
            this.flipResolve = resolve;
        });
    }

    stopFlipping() {
        this.isFlipping = false;
        if (this.flipResolve) {
            this.flipResolve();
            this.flipResolve = null;
        }
    }

    getCurrentResult() {
        // Determine if coin is showing heads or tails based on Y rotation
        const normalizedRotation = ((this.coinRotationY % (Math.PI * 2)) + (Math.PI * 2)) % (Math.PI * 2);
        return normalizedRotation < Math.PI ? 'heads' : 'tails';
    }

    destroy() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
        
        // Clean up Three.js resources
        this.scene.clear();
        this.renderer.dispose();
    }
}

// Export for use in main script
window.CoinRenderer = CoinRenderer;
