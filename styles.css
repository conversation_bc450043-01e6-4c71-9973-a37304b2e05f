/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Orbitron', monospace;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    color: #ffffff;
    min-height: 100vh;
    overflow-x: hidden;
}

/* Game Container */
.game-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.game-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: rgba(255, 215, 0, 0.1);
    border-radius: 15px;
    border: 2px solid #ffd700;
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
}

.game-title {
    font-size: 2.5rem;
    font-weight: 900;
    color: #ffd700;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    margin-bottom: 15px;
    letter-spacing: 2px;
}

.balance-display {
    font-size: 1.5rem;
    font-weight: 700;
}

.balance-label {
    color: #cccccc;
    margin-right: 10px;
}

.balance-amount {
    color: #00ff88;
    text-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
}

/* Main Game Area */
.game-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 30px;
}

/* Coin Container */
.coin-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    position: relative;
}

.coin-canvas {
    width: 200px;
    height: 200px;
    cursor: pointer;
    border-radius: 10px;
    background: transparent;
}

/* Three.js Canvas Responsive Design */
.coin-canvas {
    max-width: 100%;
    max-height: 100%;
}

/* Betting Section */
.betting-section {
    background: rgba(255, 255, 255, 0.05);
    padding: 30px;
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.section-title {
    text-align: center;
    font-size: 1.8rem;
    color: #ffd700;
    margin-bottom: 25px;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
}

/* Bet Amount */
.bet-amount-container {
    margin-bottom: 25px;
}

.bet-label {
    display: block;
    font-size: 1.1rem;
    margin-bottom: 10px;
    color: #cccccc;
}

.bet-input-group {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.currency-symbol {
    background: #ffd700;
    color: #000;
    padding: 12px 15px;
    border-radius: 8px 0 0 8px;
    font-weight: 700;
    font-size: 1.1rem;
}

.bet-input {
    flex: 1;
    padding: 12px 15px;
    border: none;
    border-radius: 0 8px 8px 0;
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    font-family: 'Orbitron', monospace;
    font-size: 1.1rem;
    outline: none;
    transition: background 0.3s ease;
}

.bet-input:focus {
    background: rgba(255, 255, 255, 0.2);
}

.quick-bet-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.quick-bet-btn {
    padding: 8px 16px;
    background: rgba(255, 215, 0, 0.2);
    border: 1px solid #ffd700;
    border-radius: 6px;
    color: #ffd700;
    font-family: 'Orbitron', monospace;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.quick-bet-btn:hover {
    background: rgba(255, 215, 0, 0.4);
    transform: translateY(-2px);
}

/* Choice Selection */
.choice-container {
    margin-bottom: 25px;
}

.choice-title {
    text-align: center;
    font-size: 1.2rem;
    margin-bottom: 15px;
    color: #cccccc;
}

.choice-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
}

.choice-btn {
    flex: 1;
    max-width: 200px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid transparent;
    border-radius: 12px;
    color: #ffffff;
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.choice-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-3px);
}

.choice-btn.selected {
    border-color: #ffd700;
    background: rgba(255, 215, 0, 0.2);
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
}

.choice-icon {
    font-size: 2rem;
}

.choice-text {
    font-size: 1.1rem;
}

/* Flip Button */
.flip-btn {
    width: 100%;
    padding: 20px;
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    border: none;
    border-radius: 12px;
    color: #ffffff;
    font-family: 'Orbitron', monospace;
    font-size: 1.3rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.flip-btn:hover:not(:disabled) {
    background: linear-gradient(45deg, #ee5a24, #ff6b6b);
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(255, 107, 107, 0.3);
}

.flip-btn:disabled {
    background: #666666;
    cursor: not-allowed;
    opacity: 0.6;
}

.flip-icon {
    font-size: 1.5rem;
}

/* Result Section */
.result-section {
    background: rgba(255, 255, 255, 0.05);
    padding: 25px;
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    text-align: center;
    backdrop-filter: blur(10px);
}

.result-title {
    font-size: 1.5rem;
    color: #ffd700;
    margin-bottom: 15px;
}

.result-message {
    font-size: 1.1rem;
    margin-bottom: 10px;
    color: #cccccc;
}

.result-amount {
    font-size: 1.3rem;
    font-weight: 700;
    margin-top: 10px;
}

.result-amount.win {
    color: #00ff88;
    text-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
}

.result-amount.lose {
    color: #ff4757;
    text-shadow: 0 0 10px rgba(255, 71, 87, 0.5);
}

/* Stats Section */
.stats-section {
    background: rgba(255, 255, 255, 0.05);
    padding: 25px;
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.stats-title {
    text-align: center;
    font-size: 1.5rem;
    color: #ffd700;
    margin-bottom: 20px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
}

.stat-label {
    color: #cccccc;
    font-size: 0.9rem;
}

.stat-value {
    color: #ffffff;
    font-weight: 700;
    font-size: 1rem;
}

/* Footer */
.game-footer {
    text-align: center;
    margin-top: 30px;
    padding: 20px;
}

.reset-btn {
    padding: 12px 24px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    color: #ffffff;
    font-family: 'Orbitron', monospace;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 15px;
}

.reset-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.disclaimer {
    font-size: 0.9rem;
    color: #888888;
    font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
    .game-title {
        font-size: 2rem;
    }

    .balance-display {
        font-size: 1.2rem;
    }

    .coin-canvas {
        width: 150px;
        height: 150px;
    }

    .choice-buttons {
        flex-direction: column;
        gap: 15px;
    }

    .choice-btn {
        max-width: none;
    }

    .quick-bet-buttons {
        justify-content: center;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .game-container {
        padding: 15px;
    }

    .game-title {
        font-size: 1.5rem;
    }

    .betting-section,
    .result-section,
    .stats-section {
        padding: 20px;
    }

    .coin-canvas {
        width: 120px;
        height: 120px;
    }
}

/* Animation Effects */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.pulse {
    animation: pulse 0.6s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.shake {
    animation: shake 0.5s ease-in-out;
}

@keyframes glow {
    0% { box-shadow: 0 0 5px rgba(255, 215, 0, 0.3); }
    50% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.8); }
    100% { box-shadow: 0 0 5px rgba(255, 215, 0, 0.3); }
}

.glow {
    animation: glow 1s ease-in-out infinite;
}
