# 🎰 Casino Coin Flipper Game

A casino-style coin flipper game built with HTML5, CSS3, and JavaScript. This is a single-page application that provides an engaging gambling experience with realistic coin flip animations, sound effects, and a complete betting system.

## 🎮 Features

### Core Gameplay
- **Interactive Coin Flip Animation**: Realistic 3D coin flip with Three.js physics engine
- **Heads/Tails Betting System**: Place bets before flipping the coin
- **Virtual Currency**: Start with $1000 and track your winnings/losses
- **Sound Effects**: Programmatically generated audio for coin flips, wins, and losses
- **Visual Feedback**: Animated results with glow effects for wins and shake effects for losses

### User Interface
- **Casino-themed Design**: Gold and blue color scheme with elegant typography
- **Responsive Layout**: Works seamlessly on desktop and mobile devices
- **Quick Bet Buttons**: Easily select common bet amounts ($10, $25, $50, $100)
- **Real-time Statistics**: Track games played, win rate, and total winnings
- **Local Storage**: Game progress is automatically saved between sessions

### Technical Features
- **Three.js 3D Graphics**: Advanced 3D coin rendering with realistic physics
- **Web Audio API**: Generates sounds programmatically (no audio files needed)
- **Realistic Physics**: Bounce effects, rotation deceleration, and natural tilt animations
- **Local Storage Persistence**: Your balance and stats are saved automatically
- **Mobile Responsive**: Optimized for all screen sizes

## 🚀 How to Run

1. **Download the files** to your local machine
2. **Open `index.html`** in any modern web browser
3. **Start playing!** No server setup or installation required

### File Structure
```
coin_flipper/
├── index.html          # Main HTML file
├── styles.css          # CSS styling and responsive design
├── script.js           # Main game logic
├── script.ts           # TypeScript source (for development)
├── coin-renderer.js    # Three.js 3D coin rendering and physics
├── audio-manager.js    # Programmatic sound generation
└── README.md           # This documentation
```

## 🎯 How to Play

1. **Set Your Bet**: Enter a bet amount or use the quick bet buttons
2. **Choose Your Side**: Click either "HEADS" or "TAILS"
3. **Flip the Coin**: Click the "FLIP COIN" button to start the animation
4. **Watch the Result**: The coin will flip and land on either heads or tails
5. **Collect Winnings**: If you guessed correctly, your bet amount is added to your balance
6. **Play Again**: Continue playing or reset the game to start over

### Game Rules
- **Starting Balance**: $1000
- **Minimum Bet**: $1
- **Maximum Bet**: Your current balance
- **Payout**: 1:1 (win your bet amount)
- **Win Condition**: Correctly guess heads or tails

## 🎨 Design Features

### Visual Elements
- **3D Coin Rendering**: Three.js powered realistic 3D coin with proper lighting and shadows
- **Physics Simulation**: Bounce effects, rotation deceleration, and natural tilt movements
- **Casino Theme**: Gold accents and dark blue background
- **Responsive Grid**: Adapts to different screen sizes
- **Visual Feedback**: Glow effects for wins, shake effects for losses

### Sound Design
- **Coin Flip Sound**: Metallic clink generated with oscillators
- **Win Sound**: Ascending chime progression
- **Lose Sound**: Descending tone

### Animations
- **Realistic Coin Flip**: 3D physics simulation with multiple rotations and natural landing
- **Bounce Physics**: Multiple decreasing bounces when the coin lands
- **Rotation Deceleration**: Gradual slowdown of coin rotation for realistic motion
- **Random Tilt Effects**: Natural wobble and unpredictable movement during flight
- **Pulse Effect**: Click the coin for a fun pulse animation
- **Result Feedback**: Glow and shake animations for game outcomes

## 🎯 Three.js Physics Features

The coin flip animation uses advanced Three.js rendering with realistic physics simulation:

### Realistic Physics Effects
- **Bounce Mechanics**: Multiple decreasing bounces with proper damping
- **Rotation Deceleration**: Gradual slowdown of angular velocity using damping factors
- **Random Tilt**: Unpredictable wobble effects during flight for natural movement
- **Gravity Simulation**: Proper gravitational acceleration and velocity calculations

### 3D Rendering Features
- **Textured Coin**: Procedurally generated heads (gold) and tails (silver) textures
- **Dynamic Lighting**: Ambient, directional, and point lights for realistic appearance
- **Shadow Mapping**: Soft shadows for enhanced depth perception
- **Responsive Canvas**: Automatically adjusts to different screen sizes

### Animation Timeline
1. **Launch Phase**: Initial upward velocity with random angular momentum
2. **Flight Phase**: Continuous rotation with gravity affecting trajectory
3. **Landing Phase**: Bounce detection and velocity damping
4. **Settling Phase**: Final position with rotation deceleration to rest

## 🔧 Technical Implementation

### Technologies Used
- **HTML5**: Semantic structure and accessibility
- **CSS3**: Responsive design, gradients, and visual effects
- **Three.js**: 3D graphics rendering and physics simulation
- **JavaScript ES6+**: Modern syntax with classes and async/await
- **Web Audio API**: Programmatic sound generation
- **Local Storage API**: Data persistence

### Key Components
- **CoinFlipperGame Class**: Main game logic and state management
- **CoinRenderer Class**: Three.js 3D coin rendering and physics simulation
- **AudioManager Class**: Sound generation and audio context management
- **Responsive Design**: Mobile-first approach with media queries

## 🎲 Game Statistics

The game tracks the following statistics:
- **Games Played**: Total number of coin flips
- **Games Won**: Number of correct guesses
- **Win Rate**: Percentage of games won
- **Total Winnings**: Net profit/loss from all games

## 🔄 Reset Functionality

- Click the "Reset Game" button to start over
- Confirmation dialog prevents accidental resets
- Resets balance to $1000 and clears all statistics
- Removes any active animations or selections

## 📱 Mobile Compatibility

The game is fully responsive and includes:
- Touch-friendly button sizes
- Optimized layouts for small screens
- Reduced coin size on mobile devices
- Simplified navigation for touch interfaces

## 🎵 Audio Features

Since the game doesn't require external audio files, it uses the Web Audio API to generate:
- **Coin flip sounds**: Metallic clink with frequency modulation
- **Win sounds**: Pleasant ascending chime sequence
- **Lose sounds**: Descending tone to indicate loss

Audio automatically resumes when the user interacts with the game (required by browser policies).

## 🛠️ Development Notes

### TypeScript Support
- `script.ts` contains the TypeScript source code
- `script.js` is the compiled JavaScript version
- Both files are functionally identical

### Browser Compatibility
- Modern browsers with ES6+ support
- WebGL support for Three.js rendering
- Web Audio API support (most browsers since 2014)
- Local Storage support

### Performance Optimizations
- Hardware-accelerated Three.js rendering
- Efficient physics calculations with requestAnimationFrame
- Minimal DOM manipulation
- Debounced audio context management
- Optimized for 60fps 3D animations

## 🎯 Future Enhancements

Potential improvements for future versions:
- Multiple coin designs and themes
- Achievement system
- Multiplayer functionality
- Progressive jackpots
- Additional betting options (side bets, streaks)
- Detailed game history
- Export/import save data

## 📄 License

This project is open source and available under the MIT License. Feel free to modify and distribute as needed.

## 🎰 Disclaimer

This is a game for entertainment purposes only. No real money is involved. Please gamble responsibly in real-world scenarios.
