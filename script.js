// Casino Coin Flipper Game Logic
class CoinFlipperGame {
    constructor() {
        this.state = {
            balance: 1000,
            currentBet: 10,
            selectedChoice: null,
            isFlipping: false,
            gamesPlayed: 0,
            gamesWon: 0,
            totalWinnings: 0
        };

        this.audioManager = new AudioManager();
        this.elements = this.getElements();
        this.coinRenderer = new CoinRenderer('coinCanvas');
        this.loadGameState();
        this.initializeEventListeners();
        this.updateDisplay();
    }

    getElements() {
        return {
            balance: document.getElementById('balance'),
            betAmount: document.getElementById('betAmount'),
            headsBtn: document.getElementById('headsBtn'),
            tailsBtn: document.getElementById('tailsBtn'),
            flipBtn: document.getElementById('flipBtn'),
            coinCanvas: document.getElementById('coinCanvas'),
            resultSection: document.getElementById('resultSection'),
            resultTitle: document.getElementById('resultTitle'),
            resultMessage: document.getElementById('resultMessage'),
            resultAmount: document.getElementById('resultAmount'),
            gamesPlayed: document.getElementById('gamesPlayed'),
            gamesWon: document.getElementById('gamesWon'),
            winRate: document.getElementById('winRate'),
            totalWinnings: document.getElementById('totalWinnings'),
            resetBtn: document.getElementById('resetBtn')
        };
    }

    initializeEventListeners() {
        // Bet amount input
        this.elements.betAmount.addEventListener('input', () => {
            this.handleBetAmountChange();
        });

        // Quick bet buttons
        document.querySelectorAll('.quick-bet-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const amount = parseInt(e.target.dataset.amount || '10');
                this.setQuickBet(amount);
            });
        });

        // Choice buttons
        this.elements.headsBtn.addEventListener('click', () => {
            this.selectChoice('heads');
        });

        this.elements.tailsBtn.addEventListener('click', () => {
            this.selectChoice('tails');
        });

        // Flip button
        this.elements.flipBtn.addEventListener('click', () => {
            this.flipCoin();
        });

        // Reset button
        this.elements.resetBtn.addEventListener('click', () => {
            this.resetGame();
        });

        // Canvas click for pulse effect (handled by CoinRenderer)
        // The CoinRenderer handles click events internally
    }

    handleBetAmountChange() {
        const betValue = parseInt(this.elements.betAmount.value);
        if (betValue > 0 && betValue <= this.state.balance) {
            this.state.currentBet = betValue;
        } else if (betValue > this.state.balance) {
            this.elements.betAmount.value = this.state.balance.toString();
            this.state.currentBet = this.state.balance;
        }
        this.updateFlipButtonState();
    }

    setQuickBet(amount) {
        if (amount <= this.state.balance) {
            this.state.currentBet = amount;
            this.elements.betAmount.value = amount.toString();
            this.updateFlipButtonState();
        }
    }

    selectChoice(choice) {
        this.state.selectedChoice = choice;
        
        // Update button states
        this.elements.headsBtn.classList.toggle('selected', choice === 'heads');
        this.elements.tailsBtn.classList.toggle('selected', choice === 'tails');
        
        this.updateFlipButtonState();
    }

    updateFlipButtonState() {
        const canFlip = this.state.selectedChoice !== null && 
                       this.state.currentBet > 0 && 
                       this.state.currentBet <= this.state.balance &&
                       !this.state.isFlipping;
        
        this.elements.flipBtn.disabled = !canFlip;
    }

    async flipCoin() {
        if (this.state.isFlipping || !this.state.selectedChoice) return;

        this.state.isFlipping = true;
        this.updateFlipButtonState();

        // Resume audio context and play flip sound
        this.audioManager.resumeAudioContext();
        this.audioManager.generateFlipSound();

        // Generate random result
        const result = Math.random() < 0.5 ? 'heads' : 'tails';
        const isWin = result === this.state.selectedChoice;

        // Animate coin flip with Three.js
        await this.coinRenderer.flipCoin(result);

        // Update game state
        this.state.gamesPlayed++;
        
        if (isWin) {
            this.state.gamesWon++;
            this.state.balance += this.state.currentBet;
            this.state.totalWinnings += this.state.currentBet;
            this.audioManager.generateWinSound();
        } else {
            this.state.balance -= this.state.currentBet;
            this.audioManager.generateLoseSound();
        }

        // Display result
        this.displayResult(result, isWin);

        // Reset for next game
        this.state.isFlipping = false;
        this.state.selectedChoice = null;
        this.elements.headsBtn.classList.remove('selected');
        this.elements.tailsBtn.classList.remove('selected');

        // Save game state
        this.saveGameState();
        this.updateDisplay();
        this.updateFlipButtonState();

        // Check if player is out of money
        if (this.state.balance <= 0) {
            this.handleGameOver();
        }
    }

    displayResult(result, isWin) {
        const resultIcon = result === 'heads' ? '👑' : '🦅';
        const resultText = result.toUpperCase();
        
        this.elements.resultTitle.textContent = `${resultIcon} ${resultText}!`;
        
        if (isWin) {
            this.elements.resultMessage.textContent = '🎉 Congratulations! You won!';
            this.elements.resultAmount.textContent = `+$${this.state.currentBet}`;
            this.elements.resultAmount.className = 'result-amount win';
            this.elements.resultSection.classList.add('glow');
        } else {
            this.elements.resultMessage.textContent = '😔 Better luck next time!';
            this.elements.resultAmount.textContent = `-$${this.state.currentBet}`;
            this.elements.resultAmount.className = 'result-amount lose';
            this.elements.resultSection.classList.add('shake');
        }

        // Remove animation classes after animation
        setTimeout(() => {
            this.elements.resultSection.classList.remove('glow', 'shake');
        }, 1000);
    }

    handleGameOver() {
        this.elements.resultTitle.textContent = '💸 Game Over!';
        this.elements.resultMessage.textContent = 'You\'re out of money! Click Reset to start over.';
        this.elements.resultAmount.textContent = '';
        this.elements.flipBtn.disabled = true;
    }

    updateDisplay() {
        this.elements.balance.textContent = `$${this.state.balance}`;
        this.elements.gamesPlayed.textContent = this.state.gamesPlayed.toString();
        this.elements.gamesWon.textContent = this.state.gamesWon.toString();
        
        const winRate = this.state.gamesPlayed > 0 
            ? Math.round((this.state.gamesWon / this.state.gamesPlayed) * 100)
            : 0;
        this.elements.winRate.textContent = `${winRate}%`;
        
        this.elements.totalWinnings.textContent = `$${this.state.totalWinnings}`;
        
        // Update bet amount max
        this.elements.betAmount.max = this.state.balance.toString();
    }



    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    saveGameState() {
        try {
            localStorage.setItem('coinFlipperGame', JSON.stringify(this.state));
        } catch (error) {
            console.warn('Could not save game state to localStorage');
        }
    }

    loadGameState() {
        try {
            const saved = localStorage.getItem('coinFlipperGame');
            if (saved) {
                const savedState = JSON.parse(saved);
                this.state = { ...this.state, ...savedState };
                this.state.selectedChoice = null; // Reset selection
                this.state.isFlipping = false; // Reset flipping state
            }
        } catch (error) {
            console.warn('Could not load game state from localStorage');
        }
    }

    resetGame() {
        if (confirm('Are you sure you want to reset the game? This will clear all your progress.')) {
            this.state = {
                balance: 1000,
                currentBet: 10,
                selectedChoice: null,
                isFlipping: false,
                gamesPlayed: 0,
                gamesWon: 0,
                totalWinnings: 0
            };
            
            this.elements.betAmount.value = '10';
            this.elements.headsBtn.classList.remove('selected');
            this.elements.tailsBtn.classList.remove('selected');
            // Reset coin renderer (no CSS classes needed for Three.js)
            
            this.elements.resultTitle.textContent = 'Result';
            this.elements.resultMessage.textContent = 'Make your bet and flip the coin!';
            this.elements.resultAmount.textContent = '';
            this.elements.resultAmount.className = 'result-amount';
            
            this.saveGameState();
            this.updateDisplay();
            this.updateFlipButtonState();
        }
    }
}

// Initialize game when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new CoinFlipperGame();
});
