<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Coin Flipper Demo Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f0f0f0;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .success { color: green; }
        .error { color: red; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>🎰 Casino Coin Flipper - Demo Test</h1>
    
    <div class="test-section">
        <h2>Game Features Verification</h2>
        <div id="test-results"></div>
        <button onclick="runTests()">Run Tests</button>
    </div>

    <div class="test-section">
        <h2>Three.js Test</h2>
        <p>Test the 3D coin renderer:</p>
        <canvas id="testCanvas" width="200" height="200" style="border: 1px solid #ccc; border-radius: 8px;"></canvas>
        <br><br>
        <button onclick="testCoinFlip()">🪙 Test Coin Flip</button>
        <p id="testResult"></p>
    </div>

    <div class="test-section">
        <h2>Audio Test</h2>
        <p>Click the buttons below to test the programmatic sound generation:</p>
        <button onclick="testFlipSound()">🪙 Coin Flip Sound</button>
        <button onclick="testWinSound()">🎉 Win Sound</button>
        <button onclick="testLoseSound()">😔 Lose Sound</button>
    </div>

    <div class="test-section">
        <h2>Game Demo</h2>
        <p>The main game is available in <a href="index.html" target="_blank">index.html</a></p>
        <p>Features included:</p>
        <ul>
            <li>✅ Three.js 3D coin flip animation with realistic physics</li>
            <li>✅ Bounce effects and rotation deceleration</li>
            <li>✅ Random tilt and wobble during flight</li>
            <li>✅ Heads/Tails betting system</li>
            <li>✅ Virtual currency ($1000 starting balance)</li>
            <li>✅ Sound effects (Web Audio API)</li>
            <li>✅ Responsive design</li>
            <li>✅ Local storage persistence</li>
            <li>✅ Game statistics tracking</li>
            <li>✅ Casino-themed UI</li>
        </ul>
    </div>

    <!-- Three.js CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="audio-manager.js"></script>
    <script src="coin-renderer.js"></script>
    <script>
        let audioManager;
        let testCoinRenderer;

        function initAudio() {
            if (!audioManager) {
                audioManager = new AudioManager();
            }
            audioManager.resumeAudioContext();
        }

        function initTestCoin() {
            if (!testCoinRenderer && window.CoinRenderer) {
                testCoinRenderer = new CoinRenderer('testCanvas');
            }
        }

        async function testCoinFlip() {
            initTestCoin();
            if (testCoinRenderer) {
                const result = Math.random() < 0.5 ? 'heads' : 'tails';
                document.getElementById('testResult').textContent = `Flipping... Result will be: ${result}`;
                await testCoinRenderer.flipCoin(result);
                document.getElementById('testResult').textContent = `✅ Coin flip completed! Result: ${result}`;
            } else {
                document.getElementById('testResult').textContent = '❌ Three.js not loaded or CoinRenderer not available';
            }
        }

        function testFlipSound() {
            initAudio();
            audioManager.generateFlipSound();
        }

        function testWinSound() {
            initAudio();
            audioManager.generateWinSound();
        }

        function testLoseSound() {
            initAudio();
            audioManager.generateLoseSound();
        }

        function runTests() {
            const results = document.getElementById('test-results');
            let output = '<h3>Test Results:</h3>';
            
            // Test 1: Check if files exist
            const requiredFiles = ['index.html', 'styles.css', 'script.js', 'audio-manager.js'];
            output += '<p><strong>File Structure Test:</strong></p><ul>';
            
            // Test 2: Check localStorage functionality
            try {
                localStorage.setItem('test', 'value');
                localStorage.removeItem('test');
                output += '<li class="success">✅ Local Storage: Working</li>';
            } catch (e) {
                output += '<li class="error">❌ Local Storage: Not available</li>';
            }
            
            // Test 3: Check Web Audio API
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                output += '<li class="success">✅ Web Audio API: Supported</li>';
            } catch (e) {
                output += '<li class="error">❌ Web Audio API: Not supported</li>';
            }
            
            // Test 4: Check WebGL support
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            if (gl) {
                output += '<li class="success">✅ WebGL (Three.js): Supported</li>';
            } else {
                output += '<li class="error">❌ WebGL (Three.js): Not supported</li>';
            }
            
            // Test 5: Check random number generation
            const randomTest = Math.random();
            if (randomTest >= 0 && randomTest < 1) {
                output += '<li class="success">✅ Random Number Generation: Working</li>';
            } else {
                output += '<li class="error">❌ Random Number Generation: Error</li>';
            }
            
            output += '</ul>';
            
            // Game logic test
            output += '<p><strong>Game Logic Test:</strong></p>';
            const testBalance = 1000;
            const testBet = 50;
            const testResult = Math.random() < 0.5 ? 'heads' : 'tails';
            const testChoice = 'heads';
            const isWin = testResult === testChoice;
            
            output += `<p>Simulated game: Bet $${testBet} on ${testChoice}, result was ${testResult}</p>`;
            if (isWin) {
                output += `<p class="success">Result: WIN! New balance would be $${testBalance + testBet}</p>`;
            } else {
                output += `<p class="error">Result: LOSE! New balance would be $${testBalance - testBet}</p>`;
            }
            
            results.innerHTML = output;
        }

        // Auto-run tests on page load
        document.addEventListener('DOMContentLoaded', () => {
            runTests();
            // Initialize test coin renderer after a short delay
            setTimeout(initTestCoin, 1000);
        });
    </script>
</body>
</html>
