<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Casino Coin Flipper</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
    <!-- Three.js CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
</head>
<body>
    <div class="game-container">
        <!-- Header -->
        <header class="game-header">
            <h1 class="game-title">🎰 CASINO COIN FLIPPER 🎰</h1>
            <div class="balance-display">
                <span class="balance-label">Balance:</span>
                <span class="balance-amount" id="balance">$1000</span>
            </div>
        </header>

        <!-- Game Area -->
        <main class="game-main">
            <!-- Coin Container -->
            <div class="coin-container">
                <canvas id="coinCanvas" class="coin-canvas"></canvas>
            </div>

            <!-- Betting Section -->
            <section class="betting-section">
                <h2 class="section-title">Place Your Bet</h2>
                
                <!-- Bet Amount -->
                <div class="bet-amount-container">
                    <label for="betAmount" class="bet-label">Bet Amount:</label>
                    <div class="bet-input-group">
                        <span class="currency-symbol">$</span>
                        <input type="number" id="betAmount" class="bet-input" min="1" max="1000" value="10">
                    </div>
                    <div class="quick-bet-buttons">
                        <button class="quick-bet-btn" data-amount="10">$10</button>
                        <button class="quick-bet-btn" data-amount="25">$25</button>
                        <button class="quick-bet-btn" data-amount="50">$50</button>
                        <button class="quick-bet-btn" data-amount="100">$100</button>
                    </div>
                </div>

                <!-- Choice Selection -->
                <div class="choice-container">
                    <h3 class="choice-title">Choose Your Side:</h3>
                    <div class="choice-buttons">
                        <button class="choice-btn" id="headsBtn" data-choice="heads">
                            <span class="choice-icon">👑</span>
                            <span class="choice-text">HEADS</span>
                        </button>
                        <button class="choice-btn" id="tailsBtn" data-choice="tails">
                            <span class="choice-icon">🦅</span>
                            <span class="choice-text">TAILS</span>
                        </button>
                    </div>
                </div>

                <!-- Flip Button -->
                <button class="flip-btn" id="flipBtn" disabled>
                    <span class="flip-text">FLIP COIN</span>
                    <span class="flip-icon">🪙</span>
                </button>
            </section>

            <!-- Result Display -->
            <section class="result-section" id="resultSection">
                <div class="result-content">
                    <h3 class="result-title" id="resultTitle">Result</h3>
                    <p class="result-message" id="resultMessage">Make your bet and flip the coin!</p>
                    <div class="result-amount" id="resultAmount"></div>
                </div>
            </section>

            <!-- Game Stats -->
            <section class="stats-section">
                <h3 class="stats-title">Game Statistics</h3>
                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-label">Games Played:</span>
                        <span class="stat-value" id="gamesPlayed">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Games Won:</span>
                        <span class="stat-value" id="gamesWon">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Win Rate:</span>
                        <span class="stat-value" id="winRate">0%</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Total Winnings:</span>
                        <span class="stat-value" id="totalWinnings">$0</span>
                    </div>
                </div>
            </section>
        </main>

        <!-- Footer -->
        <footer class="game-footer">
            <button class="reset-btn" id="resetBtn">Reset Game</button>
            <p class="disclaimer">🎲 Play responsibly. This is for entertainment only. 🎲</p>
        </footer>
    </div>

    <script src="audio-manager.js"></script>
    <script src="coin-renderer.js"></script>
    <script src="script.js"></script>
</body>
</html>
